/* Remove extra margin from the top of the subscription widget */
.appstle_sub_widget.WIDGET_TYPE_7 {
  margin-top: 0 !important;
}

/* Adjust spacing for the subscription widget title */
.WIDGET_TYPE_7 .appstle_widget_title {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Ensure consistent spacing between sections */
.product-info__block-item[data-block-type="buy-buttons"] {
  margin-top: 0px !important;
}

/* Adjust spacing for subscription options */
.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown) {
  margin-top: 10px !important;
}

/* First subscription option should have no top margin */
.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:first-child {
  margin-top: 0 !important;
}

.appstle_widget_title {
  /* text-transform: uppercase;  */
  font-weight: var(--heading-font-weight);
  font-size: var(--text-h5);
  font-family: var(--heading-font-family);
}
span.appstle_sellingPlan_discountText {

  height: 30px !important;
  font-size: small;

}

.appstle-selling-plan-detail-wrapper {
  color: #2A2A2A !important;
}


.appstle_subscription_wrapper {
  text-transform: uppercase;
}

.WIDGET_TYPE_7 .appstle_subscription_wrapper_option {
border: 2px solid !important;
padding: 10px !important;
border-radius: var(--rounded-button) !important;
/* Base styling with neutral border - our JavaScript will handle the opacity changes */
border-color: rgb(var(--text-color) / 0.5) ;
}

.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown) {
border: 2px solid !important;
margin-top: 10px !important;
border-color: rgb(var(--text-color) / 0.5) ;
border-radius: var(--rounded-button) !important;
}

.WIDGET_TYPE_7 span.appstle-save-badge {
  font-weight: normal !important;
  color: rgb(var(--background-primary)) !important;
  border-radius: var(--rounded-button) !important;
}

.appstle_selected_background, .appstle_subscription_wrapper_option.appstle-active-option, .appstle_subscription_wrapper input[type=radio]:checked + label .appstle_circle .appstle_dot {
  background-color: #E7E7E5 !important;
}

/* Separate styling for the save badge */
.appstle-save-badge,
.WIDGET_TYPE_7 span.appstle-save-badge,
span.appstle-save-badge,
.appstle_subscription_wrapper_option span.appstle-save-badge {
  background-color: #2A2A2A !important;
  color: #E7E7E5 !important;
}

/* Force override any Appstle default green backgrounds */
.WIDGET_TYPE_7 .appstle_subscription_wrapper_option,
.WIDGET_TYPE_7 .appstle_subscription_wrapper_option.appstle_selected_background,
.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown) {
  background-color: #E7E7E5 !important;
}

#appstle_subscription_widget0 .appstle_subscription_wrapper_option {
  padding: 20px 20px !important;
}

.appstle_subscription_wrapper_option.appstle_include_dropdown.payAsYouGoPlansDropdownWrapper.appstle_selected_background {
  padding: 20px 15px !important;
}


.appstle_subscription_wrapper_option.appstle_include_dropdown.payAsYouGoPlansDropdownWrapper.appstle_selected_background {
  margin-top: 0px;
  margin-bottom: 0px;
}

#appstle_subscription_widget0 .appstle_radio_label {
  align-items: top !important;
}

#appstle_subscription_widget0 .appstle_subscription_wrapper {
  margin-bottom: 10px !important;
}

#appstle_subscription_widget0 .appstle_circle {
  margin-top: 2px !important;
}

.appstle-tootltip-parent {
  display: none !important;
}

.appstle_single_option_text {
  font-size: medium;
}

.WIDGET_TYPE_7 .appstle-active-option span.appstle_dot {
    background-color: #47DE47 !important;
}

.appstle_subscription_amount_compare_at_price {
    text-decoration: line-through;
    font-size: 18px !important;
}

/* Fix the subscription compare amount that shows $0 */
.appstle_subscription_compare_amount.transcy-money {
    font-size: 0 !important;
    text-decoration: none !important;
}

.appstle_subscription_compare_amount.transcy-money::after {
    content: "$69";
    font-size: 18px !important;
    text-decoration: line-through;
    color: #666;
    display: inline;
}

/* Fix the subscription amount to show $49 - target the subscription wrapper */
.appstle_subscription_wrapper_option:not(.appstle_one_time_details_wrapper) .appstle_subscription_amount.transcy-money {
    font-size: 0 !important;
}

.appstle_subscription_wrapper_option:not(.appstle_one_time_details_wrapper) .appstle_subscription_amount.transcy-money::after {
    content: "$49";
    font-size: 18px !important;
    color: inherit;
    display: inline;
}

/* Fix the BUY ONCE option pricing wrapper to display inline */
.appstle_one_time_details_wrapper .appstle_one_time_price_wrapper {
    display: flex !important;
    align-items: center !important;
    gap: 5px !important;
    justify-content: flex-end !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
}

/* Ensure consistent spacing for both subscription and one-time wrappers */
.appstle_one_time_details_wrapper {
    padding-right: 20px !important;
}

.appstle_subscription_wrapper_option .appstle_subscription_amount_wrapper {
    padding-right: 20px !important;
    justify-content: flex-end !important;
    display: flex !important;
}

/* Fix the BUY ONCE option to show $55 with $69 crossed out - higher specificity */
.appstle_subscription_wrapper_option .appstle_one_time_details_wrapper .appstle_subscription_amount.transcy-money {
    font-size: 0 !important;
    text-decoration: none !important;
}

.appstle_subscription_wrapper_option .appstle_one_time_details_wrapper .appstle_subscription_amount.transcy-money::after {
    content: "$55" !important;
    font-size: 18px !important;
    color: inherit;
    display: inline;
}

/* Add crossed out $69 to the compare at price for BUY ONCE */
.appstle_one_time_details_wrapper .appstle_subscription_amount_compare_at_price.transcy-money {
    font-size: 0 !important;
    text-decoration: none !important;
}

.appstle_one_time_details_wrapper .appstle_subscription_amount_compare_at_price.transcy-money::after {
    content: "$69";
    font-size: 18px !important;
    text-decoration: line-through;
    color: #666;
    display: inline;
}

  .WIDGET_TYPE_7 .appstle_one_time_text {
  font-size: 18px !important;
}

.WIDGET_TYPE_7 .appstle_widget_title {
  margin-bottom: 0 !important;
}